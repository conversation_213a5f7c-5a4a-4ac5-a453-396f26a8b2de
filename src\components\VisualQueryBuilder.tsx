"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, memo } from "react";
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Chip,
  Textarea,
  Tabs,
  Tab,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
} from "@heroui/react";
import {
  Plus,
  Trash2,
  Eye,
  Code,
  Play,
  Settings,
  Maximize,
  Minimize,
} from "lucide-react";
import {
  VisualQueryConfig,
  TableRelationship,
  ColumnSelection,
  WhereCondition,
  DatabaseOperationType,
  JoinType,
  AggregationFunction,
} from "@/lib/jobManager";

// Constants for better performance
const OPERATION_TYPES = [
  { key: "CREATE_TABLE_AS_SELECT", label: "Create Table As Select" },
  { key: "DROP_TABLE", label: "Drop Table" },
  { key: "DATA_EXTRACTION", label: "Data Extraction" },
] as const;

const JOIN_TYPES = [
  { key: "INNER", label: "INNER" },
  { key: "LEFT", label: "LEFT" },
  { key: "RIGHT", label: "RIGHT" },
  { key: "FULL", label: "FULL" },
] as const;

const AGGREGATION_FUNCTIONS = [
  { key: "SUM", label: "SUM" },
  { key: "COUNT", label: "COUNT" },
  { key: "AVG", label: "AVG" },
  { key: "MIN", label: "MIN" },
  { key: "MAX", label: "MAX" },
] as const;

const OPERATORS = [
  { key: "=", label: "=" },
  { key: "!=", label: "!=" },
  { key: ">", label: ">" },
  { key: "<", label: "<" },
  { key: ">=", label: ">=" },
  { key: "<=", label: "<=" },
  { key: "LIKE", label: "LIKE" },
  { key: "IN", label: "IN" },
] as const;

const LOGICAL_OPERATORS = [
  { key: "AND", label: "AND" },
  { key: "OR", label: "OR" },
] as const;

// Memoized components for better performance
const TableChip = memo(({ table, onRemove }: { table: string; onRemove: (table: string) => void }) => (
  <Chip
    key={table}
    onClose={() => onRemove(table)}
    variant="flat"
    color="primary"
  >
    {table}
  </Chip>
));
TableChip.displayName = 'TableChip';

const RelationshipCard = memo(({ 
  relationship, 
  sourceTables, 
  onUpdate, 
  onRemove 
}: {
  relationship: TableRelationship;
  sourceTables: string[];
  onUpdate: (id: string, updates: Partial<TableRelationship>) => void;
  onRemove: (id: string) => void;
}) => (
  <Card key={relationship.id} className="p-3">
    <div className="grid grid-cols-5 gap-2 items-center">
      <Select
        placeholder="Source Table"
        selectedKeys={[relationship.sourceTable]}
        onSelectionChange={(keys) =>
          onUpdate(relationship.id, {
            sourceTable: Array.from(keys)[0] as string,
          })
        }
      >
        {sourceTables.map((table) => (
          <SelectItem key={table}>{table}</SelectItem>
        ))}
      </Select>
      <Input
        placeholder="Column"
        value={relationship.sourceColumn}
        onChange={(e) =>
          onUpdate(relationship.id, {
            sourceColumn: e.target.value,
          })
        }
      />
      <Select
        selectedKeys={[relationship.joinType]}
        onSelectionChange={(keys) =>
          onUpdate(relationship.id, {
            joinType: Array.from(keys)[0] as JoinType,
          })
        }
      >
        {JOIN_TYPES.map(({ key, label }) => (
          <SelectItem key={key}>{label}</SelectItem>
        ))}
      </Select>
      <Input
        placeholder="Target Column"
        value={relationship.targetColumn}
        onChange={(e) =>
          onUpdate(relationship.id, {
            targetColumn: e.target.value,
          })
        }
      />
      <Button
        size="sm"
        variant="ghost"
        isIconOnly
        color="danger"
        onPress={() => onRemove(relationship.id)}
      >
        <Trash2 className="w-4 h-4" />
      </Button>
    </div>
  </Card>
));
RelationshipCard.displayName = 'RelationshipCard';

const ColumnCard = memo(({ 
  column, 
  sourceTables, 
  onUpdate, 
  onRemove 
}: {
  column: ColumnSelection;
  sourceTables: string[];
  onUpdate: (id: string, updates: Partial<ColumnSelection>) => void;
  onRemove: (id: string) => void;
}) => (
  <Card key={column.id} className="p-3">
    <div className="grid grid-cols-6 gap-2 items-center">
      <Select
        placeholder="Table"
        selectedKeys={[column.table]}
        onSelectionChange={(keys) =>
          onUpdate(column.id, {
            table: Array.from(keys)[0] as string,
          })
        }
      >
        {sourceTables.map((table) => (
          <SelectItem key={table}>{table}</SelectItem>
        ))}
      </Select>
      <Input
        placeholder="Column"
        value={column.column}
        onChange={(e) =>
          onUpdate(column.id, {
            column: e.target.value,
          })
        }
      />
      <Select
        placeholder="Aggregation"
        selectedKeys={column.aggregation ? [column.aggregation] : []}
        onSelectionChange={(keys) =>
          onUpdate(column.id, {
            aggregation: Array.from(keys)[0] as AggregationFunction,
          })
        }
      >
        {AGGREGATION_FUNCTIONS.map(({ key, label }) => (
          <SelectItem key={key}>{label}</SelectItem>
        ))}
      </Select>
      <Input
        placeholder="Expression"
        value={column.expression || ""}
        onChange={(e) =>
          onUpdate(column.id, {
            expression: e.target.value,
          })
        }
      />
      <Input
        placeholder="Alias"
        value={column.alias || ""}
        onChange={(e) =>
          onUpdate(column.id, {
            alias: e.target.value,
          })
        }
      />
      <Button
        size="sm"
        variant="ghost"
        isIconOnly
        color="danger"
        onPress={() => onRemove(column.id)}
      >
        <Trash2 className="w-4 h-4" />
      </Button>
    </div>
  </Card>
));
ColumnCard.displayName = 'ColumnCard';

const ConditionCard = memo(({ 
  condition, 
  onUpdate, 
  onRemove 
}: {
  condition: WhereCondition;
  onUpdate: (id: string, updates: Partial<WhereCondition>) => void;
  onRemove: (id: string) => void;
}) => (
  <Card key={condition.id} className="p-3">
    <div className="grid grid-cols-5 gap-2 items-center">
      <Input
        placeholder="Column"
        value={condition.column}
        onChange={(e) =>
          onUpdate(condition.id, {
            column: e.target.value,
          })
        }
      />
      <Select
        selectedKeys={[condition.operator]}
        onSelectionChange={(keys) =>
          onUpdate(condition.id, {
            operator: Array.from(keys)[0] as WhereCondition["operator"],
          })
        }
      >
        {OPERATORS.map(({ key, label }) => (
          <SelectItem key={key}>{label}</SelectItem>
        ))}
      </Select>
      <Input
        placeholder="Value"
        value={
          Array.isArray(condition.value)
            ? condition.value.join(", ")
            : condition.value
        }
        onChange={(e) =>
          onUpdate(condition.id, {
            value: e.target.value,
          })
        }
      />
      <Select
        selectedKeys={
          condition.logicalOperator ? [condition.logicalOperator] : []
        }
        onSelectionChange={(keys) =>
          onUpdate(condition.id, {
            logicalOperator: Array.from(keys)[0] as "AND" | "OR",
          })
        }
      >
        {LOGICAL_OPERATORS.map(({ key, label }) => (
          <SelectItem key={key}>{label}</SelectItem>
        ))}
      </Select>
      <Button
        size="sm"
        variant="ghost"
        isIconOnly
        color="danger"
        onPress={() => onRemove(condition.id)}
      >
        <Trash2 className="w-4 h-4" />
      </Button>
    </div>
  </Card>
));
ConditionCard.displayName = 'ConditionCard';

interface VisualQueryBuilderProps {
  config: VisualQueryConfig;
  onChange: (config: VisualQueryConfig) => void;
  availableTables: string[];
  onPreview?: (sql: string) => void;
  className?: string;
}

const VisualQueryBuilder = memo(function VisualQueryBuilder({
  config,
  onChange,
  availableTables,
  onPreview,
  className = "",
}: VisualQueryBuilderProps) {
  const [activeTab, setActiveTab] = useState("tables");
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Memoized available tables for better performance
  const memoizedAvailableTables = useMemo(() => availableTables, [availableTables]);

  // Generate SQL from current configuration with memoization
  const generateSQL = useCallback(() => {
    let sql = "";

    if (config.operationType === "CREATE_TABLE_AS_SELECT") {
      sql += `CREATE TABLE ${config.targetTable || "new_table"} AS\n`;
    } else if (config.operationType === "DROP_TABLE") {
      return `DROP TABLE IF EXISTS ${config.targetTable || "table_name"}`;
    }

    // SELECT clause
    sql += "SELECT\n";
    if (config.selectedColumns.length > 0) {
      const columnExpressions = config.selectedColumns.map((col) => {
        let expr = `${col.table}.${col.column}`;

        if (col.aggregation) {
          expr = `${col.aggregation}(${expr})`;
        }

        if (col.expression) {
          expr = col.expression.replace("${column}", expr);
        }

        if (col.alias) {
          expr += ` AS ${col.alias}`;
        }

        return `    ${expr}`;
      });
      sql += columnExpressions.join(",\n") + "\n";
    } else {
      sql += "    *\n";
    }

    // FROM clause
    if (config.sourceTables.length > 0) {
      sql += `FROM\n    ${config.sourceTables[0]} a\n`;

      // JOIN clauses
      config.relationships.forEach((rel, index) => {
        const joinType =
          rel.joinType === "INNER"
            ? "INNER JOIN"
            : rel.joinType === "LEFT"
            ? "LEFT JOIN"
            : rel.joinType === "RIGHT"
            ? "RIGHT JOIN"
            : "FULL JOIN";

        sql += `${joinType}\n    ${rel.targetTable} ${String.fromCharCode(
          98 + index
        )} ON a.${rel.sourceColumn} = ${String.fromCharCode(98 + index)}.${
          rel.targetColumn
        }\n`;
      });
    }

    // WHERE clause
    if (config.whereConditions.length > 0) {
      sql += "WHERE\n";
      const conditions = config.whereConditions.map((cond, index) => {
        let conditionStr = `    ${cond.column} ${cond.operator} `;

        if (Array.isArray(cond.value)) {
          conditionStr += `(${cond.value.map((v) => `'${v}'`).join(", ")})`;
        } else {
          conditionStr += `'${cond.value}'`;
        }

        if (index < config.whereConditions.length - 1 && cond.logicalOperator) {
          conditionStr += ` ${cond.logicalOperator}`;
        }

        return conditionStr;
      });
      sql += conditions.join("\n") + "\n";
    }

    // GROUP BY clause
    if (config.groupByColumns.length > 0) {
      sql += `GROUP BY\n    ${config.groupByColumns.join(", ")}\n`;
    }

    // ORDER BY clause
    if (config.orderByColumns.length > 0) {
      const orderExpressions = config.orderByColumns.map(
        (col) => `${col.column} ${col.direction}`
      );
      sql += `ORDER BY\n    ${orderExpressions.join(", ")}\n`;
    }

    return sql;
  }, [config]);

  // Memoized generated SQL for better performance
  const generatedSQL = useMemo(() => generateSQL(), [generateSQL]);

  // Memoized event handlers for better performance
  const addSourceTable = useCallback((tableName: string) => {
    if (!config.sourceTables.includes(tableName)) {
      onChange({
        ...config,
        sourceTables: [...config.sourceTables, tableName],
      });
    }
  }, [config, onChange]);

  const removeSourceTable = useCallback((tableName: string) => {
    onChange({
      ...config,
      sourceTables: config.sourceTables.filter((t) => t !== tableName),
      relationships: config.relationships.filter(
        (r) => r.sourceTable !== tableName && r.targetTable !== tableName
      ),
      selectedColumns: config.selectedColumns.filter(
        (c) => c.table !== tableName
      ),
    });
  }, [config, onChange]);

  const addRelationship = useCallback(() => {
    const newRelationship: TableRelationship = {
      id: `rel_${Date.now()}`,
      sourceTable: config.sourceTables[0] || "",
      sourceColumn: "",
      targetTable: "",
      targetColumn: "",
      joinType: "INNER",
    };

    onChange({
      ...config,
      relationships: [...config.relationships, newRelationship],
    });
  }, [config, onChange]);

  const updateRelationship = useCallback((
    id: string,
    updates: Partial<TableRelationship>
  ) => {
    onChange({
      ...config,
      relationships: config.relationships.map((rel) =>
        rel.id === id ? { ...rel, ...updates } : rel
      ),
    });
  }, [config, onChange]);

  const removeRelationship = useCallback((id: string) => {
    onChange({
      ...config,
      relationships: config.relationships.filter((rel) => rel.id !== id),
    });
  }, [config, onChange]);

  const addColumnSelection = useCallback(() => {
    const newColumn: ColumnSelection = {
      id: `col_${Date.now()}`,
      table: config.sourceTables[0] || "",
      column: "",
      alias: "",
    };

    onChange({
      ...config,
      selectedColumns: [...config.selectedColumns, newColumn],
    });
  }, [config, onChange]);

  const updateColumnSelection = useCallback((
    id: string,
    updates: Partial<ColumnSelection>
  ) => {
    onChange({
      ...config,
      selectedColumns: config.selectedColumns.map((col) =>
        col.id === id ? { ...col, ...updates } : col
      ),
    });
  }, [config, onChange]);

  const removeColumnSelection = useCallback((id: string) => {
    onChange({
      ...config,
      selectedColumns: config.selectedColumns.filter((col) => col.id !== id),
    });
  }, [config, onChange]);

  const addWhereCondition = useCallback(() => {
    const newCondition: WhereCondition = {
      id: `where_${Date.now()}`,
      column: "",
      operator: "=",
      value: "",
      logicalOperator: "AND",
    };

    onChange({
      ...config,
      whereConditions: [...config.whereConditions, newCondition],
    });
  }, [config, onChange]);

  const updateWhereCondition = useCallback((
    id: string,
    updates: Partial<WhereCondition>
  ) => {
    onChange({
      ...config,
      whereConditions: config.whereConditions.map((cond) =>
        cond.id === id ? { ...cond, ...updates } : cond
      ),
    });
  }, [config, onChange]);

  const removeWhereCondition = useCallback((id: string) => {
    onChange({
      ...config,
      whereConditions: config.whereConditions.filter((cond) => cond.id !== id),
    });
  }, [config, onChange]);

  const renderContent = () => (
    <Card className={`w-full ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          <h3 className="text-lg font-semibold">Visual Query Builder</h3>
        </div>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="ghost"
            startContent={<Eye className="w-4 h-4" />}
            onPress={() => onPreview?.(generatedSQL)}
          >
            Preview
          </Button>
          <Button
            size="sm"
            color="primary"
            startContent={<Play className="w-4 h-4" />}
          >
            Execute
          </Button>
          <Button
            size="sm"
            variant="flat"
            isIconOnly
            startContent={
              isFullscreen ? (
                <Minimize className="w-4 h-4" />
              ) : (
                <Maximize className="w-4 h-4" />
              )
            }
            onPress={() => setIsFullscreen(!isFullscreen)}
            title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
          />
        </div>
      </CardHeader>

      <CardBody>
        <Tabs
          selectedKey={activeTab}
          onSelectionChange={(key) => setActiveTab(key as string)}
          className="w-full"
        >
          <Tab key="tables" title="Tables & Joins">
            <div className="space-y-4">
              {/* Operation Type */}
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Operation Type
                </label>
                <Select
                  selectedKeys={[config.operationType]}
                  onSelectionChange={(keys) => {
                    const type = Array.from(keys)[0] as DatabaseOperationType;
                    onChange({ ...config, operationType: type });
                  }}
                >
                  {OPERATION_TYPES.map(({ key, label }) => (
                    <SelectItem key={key}>{label}</SelectItem>
                  ))}
                </Select>
              </div>

              {/* Target Table (for CREATE operations) */}
              {config.operationType === "CREATE_TABLE_AS_SELECT" && (
                <Input
                  label="Target Table Name"
                  value={config.targetTable || ""}
                  onChange={(e) =>
                    onChange({ ...config, targetTable: e.target.value })
                  }
                  placeholder="new_table_name"
                />
              )}

              {/* Source Tables */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium">Source Tables</label>
                  <Select
                    placeholder="Add table"
                    onSelectionChange={(keys) => {
                      const tableName = Array.from(keys)[0] as string;
                      if (tableName) addSourceTable(tableName);
                    }}
                    className="w-48"
                  >
                    {memoizedAvailableTables.map((table) => (
                      <SelectItem key={table}>{table}</SelectItem>
                    ))}
                  </Select>
                </div>
                <div className="flex flex-wrap gap-2">
                  {config.sourceTables.map((table) => (
                    <TableChip key={table} table={table} onRemove={removeSourceTable} />
                  ))}
                </div>
              </div>

              {/* Relationships/Joins */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium">
                    Table Relationships
                  </label>
                  <Button
                    size="sm"
                    variant="ghost"
                    startContent={<Plus className="w-4 h-4" />}
                    onPress={addRelationship}
                    isDisabled={config.sourceTables.length < 2}
                  >
                    Add Join
                  </Button>
                </div>
                <div className="space-y-2">
                  {config.relationships.map((rel) => (
                    <RelationshipCard
                      key={rel.id}
                      relationship={rel}
                      sourceTables={config.sourceTables}
                      onUpdate={updateRelationship}
                      onRemove={removeRelationship}
                    />
                  ))}
                </div>
              </div>
            </div>
          </Tab>

          <Tab key="columns" title="Columns">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Selected Columns</label>
                <Button
                  size="sm"
                  variant="ghost"
                  startContent={<Plus className="w-4 h-4" />}
                  onPress={addColumnSelection}
                >
                  Add Column
                </Button>
              </div>
              <div className="space-y-2">
                {config.selectedColumns.map((col) => (
                  <ColumnCard
                    key={col.id}
                    column={col}
                    sourceTables={config.sourceTables}
                    onUpdate={updateColumnSelection}
                    onRemove={removeColumnSelection}
                  />
                ))}
              </div>
            </div>
          </Tab>

          <Tab key="conditions" title="Conditions">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">WHERE Conditions</label>
                <Button
                  size="sm"
                  variant="ghost"
                  startContent={<Plus className="w-4 h-4" />}
                  onPress={addWhereCondition}
                >
                  Add Condition
                </Button>
              </div>
              <div className="space-y-2">
                {config.whereConditions.map((cond) => (
                  <ConditionCard
                    key={cond.id}
                    condition={cond}
                    onUpdate={updateWhereCondition}
                    onRemove={removeWhereCondition}
                  />
                ))}
              </div>
            </div>
          </Tab>

          <Tab key="preview" title="SQL Preview">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Generated SQL</label>
                <Button
                  size="sm"
                  variant="ghost"
                  startContent={<Code className="w-4 h-4" />}
                  onPress={() => navigator.clipboard.writeText(generatedSQL)}
                >
                  Copy SQL
                </Button>
              </div>
              <Textarea
                value={generatedSQL}
                readOnly
                minRows={10}
                className="font-mono text-sm"
              />
            </div>
          </Tab>
        </Tabs>
      </CardBody>
    </Card>
  );

  if (isFullscreen) {
    return (
      <Modal
        isOpen={isFullscreen}
        onClose={() => setIsFullscreen(false)}
        size="full"
        scrollBehavior="inside"
        backdrop="blur"
        hideCloseButton
        classNames={{
          base: "max-h-screen",
          body: "min-h-[calc(100vh-100px)] max-h-[calc(100vh-100px)] overflow-y-auto p-0",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              <span>Visual Query Builder - Fullscreen</span>
            </div>
            <Button
              variant="flat"
              size="sm"
              isIconOnly
              startContent={<Minimize className="w-4 h-4" />}
              onPress={() => setIsFullscreen(false)}
              title="Exit Fullscreen"
            />
          </ModalHeader>
          <ModalBody>{renderContent()}</ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return renderContent();
});

VisualQueryBuilder.displayName = 'VisualQueryBuilder';

export default VisualQueryBuilder;
