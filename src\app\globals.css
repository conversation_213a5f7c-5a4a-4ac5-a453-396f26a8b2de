@import "tailwindcss";
@plugin '../hero.ts';

@source '../../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}';
@custom-variant dark (&:is(.dark *));

:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;

  /* Modern Design Tokens */
  --primary: 217 91% 60%;
  --primary-foreground: 0 0% 98%;
  --secondary: 210 40% 98%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 217 91% 60%;

  /* Spacing Scale */
  --spacing-xs: 0.25rem; /* 4px */
  --spacing-sm: 0.5rem; /* 8px */
  --spacing-md: 0.75rem; /* 12px */
  --spacing-lg: 1rem; /* 16px */
  --spacing-xl: 1.5rem; /* 24px */
  --spacing-2xl: 2rem; /* 32px */
  --spacing-3xl: 3rem; /* 48px */

  /* Border Radius */
  --radius-sm: 0.25rem; /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem; /* 8px */
  --radius-xl: 0.75rem; /* 12px */
  --radius-2xl: 1rem; /* 16px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}

:root[class~="dark"] {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --primary: 217 91% 60%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 217 91% 60%;
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-geist-sans), system-ui, sans-serif;
}

@layer utilities {
  .content-container {
    max-width: 90rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  @media (min-width: 640px) {
    .content-container {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  @media (min-width: 1024px) {
    .content-container {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  /* Modern glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Modern Card Styles - Flat Design */
  .card-modern {
    background: hsl(var(--background));
    border: 2px solid rgb(241 245 249); /* slate-100 */
    border-radius: var(--radius-2xl);
    transition: all 0.2s ease-in-out;
  }

  .card-modern:hover {
    background: hsl(var(--muted) / 0.5);
  }

  .dark .card-modern {
    border-color: rgb(51 65 85); /* slate-600 for dark mode */
  }

  .card-elevated {
    background: hsl(var(--background));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
  }

  /* Compact Spacing */
  .space-compact > * + * {
    margin-top: var(--spacing-md);
  }

  .space-tight > * + * {
    margin-top: var(--spacing-sm);
  }

  /* Modern Form Styles */
  .form-section {
    background: hsl(var(--muted) / 0.3);
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
  }

  .form-group {
    display: grid;
    gap: var(--spacing-md);
  }

  .form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    max-width: 100%;
    overflow: hidden;
  }

  /* Status Indicators */
  .status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
  }

  .status-success {
    background: hsl(142 76% 36% / 0.1);
    color: hsl(142 76% 36%);
  }

  .status-warning {
    background: hsl(38 92% 50% / 0.1);
    color: hsl(38 92% 50%);
  }

  .status-error {
    background: hsl(var(--destructive) / 0.1);
    color: hsl(var(--destructive));
  }

  /* Animation Classes */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Focus Styles */
  .focus-ring {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Modal Content Constraints */
  .modal-content-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    word-wrap: break-word;
    word-break: break-word;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .modal-form-container {
    max-width: 100%;
    overflow-x: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .modal-form-container .form-row {
    max-width: 100%;
    overflow: hidden;
  }

  .modal-form-container input,
  .modal-form-container textarea,
  .modal-form-container select {
    max-width: 100%;
    min-width: 0;
  }

  /* Ensure modal maintains fixed dimensions */
  .modal-fixed-size {
    height: 80vh !important;
    width: 64rem !important;
    max-width: 90vw !important;
    min-height: 80vh !important;
    max-height: 80vh !important;
  }

  /* Modal body should be scrollable within fixed height */
  .modal-body-fixed {
    height: calc(80vh - 160px) !important;
    min-height: calc(80vh - 160px) !important;
    max-height: calc(80vh - 160px) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }

  /* Ensure tabs don't cause overflow */
  .modal-content-container [data-slot="tablist"] {
    flex-shrink: 0;
    max-width: 100%;
    overflow-x: auto;
  }

  /* Scrollable content area within modal */
  .modal-scrollable-content {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    overflow-x: hidden;
    /* Remove asymmetric padding to ensure consistent left/right spacing */
  }

  /* Ensure grid layouts don't expand beyond modal width */
  .modal-form-container .grid {
    max-width: 100%;
    overflow: hidden;
  }

  /* Handle specific HeroUI component overflow */
  .modal-content-container [data-slot="base"],
  .modal-content-container [data-slot="wrapper"],
  .modal-form-container [data-slot="base"],
  .modal-form-container [data-slot="wrapper"] {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden;
  }

  /* Ensure job cards span full width */
  .modal-content-container .card-modern,
  .modal-content-container [data-slot="base"] {
    width: 100% !important;
    max-width: none !important;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 640px) {
    .modal-content-container .status-indicator {
      font-size: 10px;
      padding: 2px 6px;
    }

    .modal-content-container .card-modern {
      border-radius: var(--radius-2xl);
    }

    /* Reduce motion scale on mobile for better touch experience */
    .modal-content-container .card-modern:hover {
      transform: none;
    }
  }

  /* Ensure tables and wide content are responsive */
  .modal-content-container table,
  .modal-form-container table {
    max-width: 100%;
    overflow-x: auto;
    display: block;
    white-space: nowrap;
  }

  /* Handle code blocks and pre elements */
  .modal-content-container pre,
  .modal-content-container code,
  .modal-form-container pre,
  .modal-form-container code {
    max-width: 100%;
    overflow-x: auto;
    word-wrap: break-word;
  }

  /* Responsive Grid */
  .grid-responsive {
    display: grid;
    gap: var(--spacing-md);
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 640px) {
    .grid-responsive {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive {
      grid-template-columns: repeat(6, 1fr);
    }
  }

  .grid-compact {
    display: grid;
    gap: var(--spacing-sm);
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.5);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.7);
  }

  /* Override HeroUI Card default styling for flat design */
  [data-slot="base"]:has([data-slot="body"]) {
    box-shadow: none !important;
    background: hsl(var(--background));
    border: 2px solid rgb(241 245 249); /* slate-100 */
  }

  .dark [data-slot="base"]:has([data-slot="body"]) {
    border-color: rgb(51 65 85); /* slate-600 for dark mode */
  }

  /* Ensure consistent card styling across all HeroUI cards */
  .heroui-card {
    box-shadow: none !important;
    background: hsl(var(--background));
    border: 2px solid rgb(241 245 249); /* slate-100 */
  }

  .dark .heroui-card {
    border-color: rgb(51 65 85); /* slate-600 for dark mode */
  }
}
