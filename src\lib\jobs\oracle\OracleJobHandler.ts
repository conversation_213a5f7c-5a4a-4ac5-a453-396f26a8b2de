import { BaseJobHandler } from "../base/BaseJobHandler";
import type { <PERSON><PERSON><PERSON><PERSON>, JobExecutionContext } from "../types";
import type { JobDefinition } from "../../jobManager";
import { executeOracleQuery } from "../../../tarikData/db/Oracle.js";
import { logger } from "../../jobManager";

/**
 * Job handler for Oracle database operations
 */
export class OracleJobHandler extends BaseJobHandler {
  public readonly jobType = "oracle";

  /**
   * Execute Oracle database job
   */
  public async execute(context: JobExecutionContext): Promise<JobResult> {
    const { jobDefinition } = context;
    const oracleConfig = jobDefinition.dataSource.oracle;

    if (!oracleConfig) {
      throw new Error("Oracle configuration is required for Oracle data source");
    }

    // Validate configuration
    this.validateOracleConfig(oracleConfig);

    const startTime = await this.logOperationStart(
      context,
      "Oracle database connection",
      `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`
    );

    try {
      // Simulate network delay
      await this.simulateNetworkDelay(1000);
      await context.checkCancellation();

      await context.addLog("Connected to Oracle database successfully");
      await context.addLog("Executing SQL query...");

      // Execute the Oracle query
      const result = await context.executeWithCancellationCheck(
        () => executeOracleQuery(
          oracleConfig.query,
          [], // parameters array (add if needed)
          {
            user: oracleConfig.username,
            password: oracleConfig.password,
            connectString: `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`,
            maxRows: 10000, // Adjust based on needs
          }
        ),
        "Oracle query execution"
      );

      const recordCount = result.rows ? result.rows.length : 0;

      await context.addLog(
        `Query executed successfully. Retrieved ${recordCount} records`
      );

      await this.logOperationComplete(
        context,
        "Oracle database operation",
        startTime,
        `${recordCount} records retrieved`
      );

      logger.info(`Oracle query executed successfully for job ${context.jobId}`, {
        jobId: context.jobId,
        recordCount,
        query: oracleConfig.query.substring(0, 100) + "...", // Log first 100 chars of query
      });

      return this.createJobResult(recordCount, result.rows || []);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown Oracle error";

      await context.addLog(`Oracle connection failed: ${errorMessage}`);

      logger.error(`Oracle database connection failed for job ${context.jobId}`, {
        jobId: context.jobId,
        error: errorMessage,
        host: oracleConfig.host,
        port: oracleConfig.port,
        serviceName: oracleConfig.serviceName,
      });

      throw new Error(`Oracle database connection failed: ${errorMessage}`);
    }
  }

  /**
   * Validate Oracle job configuration
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    if (!super.validateConfig(jobDef)) {
      return false;
    }

    const oracleConfig = jobDef.dataSource.oracle;
    if (!oracleConfig) {
      return false;
    }

    try {
      this.validateOracleConfig(oracleConfig);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get required permissions for Oracle operations
   */
  public getRequiredPermissions(): string[] {
    return ["database:oracle:read"];
  }

  /**
   * Validate Oracle configuration fields
   */
  private validateOracleConfig(config: NonNullable<JobDefinition["dataSource"]["oracle"]>): void {
    this.validateRequiredFields(
      config,
      ["host", "port", "serviceName", "username", "password", "query"],
      "Oracle configuration"
    );

    // Additional Oracle-specific validations
    if (typeof config.port !== "number" || config.port <= 0 || config.port > 65535) {
      throw new Error("Oracle port must be a valid number between 1 and 65535");
    }

    if (!config.query.trim()) {
      throw new Error("Oracle query cannot be empty");
    }

    // Basic SQL injection prevention - check for dangerous patterns
    const dangerousPatterns = [
      /;\s*drop\s+/i,
      /;\s*delete\s+/i,
      /;\s*truncate\s+/i,
      /;\s*alter\s+/i,
      /;\s*create\s+/i
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(config.query)) {
        throw new Error("Oracle query contains potentially dangerous SQL statements");
      }
    }
  }
}
