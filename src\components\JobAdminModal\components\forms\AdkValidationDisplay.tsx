import React from "react";
import { Card, CardBody, Progress, Chip } from "@heroui/react";
import { CheckCircle, AlertTriangle, XCircle, Info } from "lucide-react";
import { JobDefinition } from "@/lib/jobManager";
import {
  validateAdkConfiguration,
  getValidationStatusColor,
  getValidationStatusMessage,
  getAdkConfigurationCompleteness,
  AdkValidationResult,
} from "../../utils/adkValidation";

interface AdkValidationDisplayProps {
  job: JobDefinition;
  className?: string;
}

export const AdkValidationDisplay: React.FC<AdkValidationDisplayProps> = ({
  job,
  className = "",
}) => {
  const validation = validateAdkConfiguration(job);
  const completeness = getAdkConfigurationCompleteness(job);
  const statusColor = getValidationStatusColor(validation);
  const statusMessage = getValidationStatusMessage(validation);

  // Don't show validation for non-ADK jobs
  if (job.dataSource.type !== "adk_processing") {
    return null;
  }

  const getStatusIcon = () => {
    switch (statusColor) {
      case "success":
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "warning":
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case "danger":
        return <XCircle className="w-5 h-5 text-red-600" />;
      default:
        return <Info className="w-5 h-5 text-blue-600" />;
    }
  };

  const getProgressColor = () => {
    if (completeness === 100) return "success";
    if (completeness >= 70) return "warning";
    return "danger";
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Configuration Status Header */}
      <Card
        className={`border ${
          statusColor === "success"
            ? "border-green-200 bg-green-50"
            : statusColor === "warning"
            ? "border-yellow-200 bg-yellow-50"
            : "border-red-200 bg-red-50"
        }`}
      >
        <CardBody className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <div>
                <h5 className="font-semibold text-gray-800">
                  ADK Configuration Status
                </h5>
                <p className="text-sm text-gray-600">{statusMessage}</p>
              </div>
            </div>
            <Chip color={statusColor} variant="flat" size="lg">
              {completeness}% Complete
            </Chip>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <Progress
              value={completeness}
              color={getProgressColor()}
              size="md"
              className="w-full"
              label="Configuration Completeness"
              showValueLabel={true}
            />
          </div>
        </CardBody>
      </Card>

      {/* Errors Section */}
      {validation.errors.length > 0 && (
        <Card className="border border-red-200 bg-red-50">
          <CardBody className="p-4">
            <div className="flex items-start gap-3">
              <XCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h6 className="font-semibold text-red-800 mb-2">
                  Configuration Errors ({validation.errors.length})
                </h6>
                <ul className="space-y-1">
                  {validation.errors.map((error, index) => (
                    <li key={index} className="text-sm text-red-700 flex items-start gap-2">
                      <div className="w-1 h-1 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Warnings Section */}
      {validation.warnings.length > 0 && (
        <Card className="border border-yellow-200 bg-yellow-50">
          <CardBody className="p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h6 className="font-semibold text-yellow-800 mb-2">
                  Recommendations ({validation.warnings.length})
                </h6>
                <ul className="space-y-1">
                  {validation.warnings.map((warning, index) => (
                    <li key={index} className="text-sm text-yellow-700 flex items-start gap-2">
                      <div className="w-1 h-1 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                      {warning}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Success Section */}
      {validation.isValid && validation.warnings.length === 0 && (
        <Card className="border border-green-200 bg-green-50">
          <CardBody className="p-4">
            <div className="flex items-start gap-3">
              <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h6 className="font-semibold text-green-800 mb-2">
                  Configuration Ready
                </h6>
                <p className="text-sm text-green-700">
                  Your ADK processing configuration is complete and ready for execution. 
                  The system will process archives from your source directory and distribute 
                  data across the 14 target tables automatically.
                </p>
                <div className="mt-3 grid grid-cols-2 gap-4 text-xs">
                  <div className="bg-white p-2 rounded border border-green-200">
                    <strong>Expected Performance:</strong><br />
                    ~173 files, ~67K records, ~0.5s per file
                  </div>
                  <div className="bg-white p-2 rounded border border-green-200">
                    <strong>Error Handling:</strong><br />
                    Continues on errors, automatic cleanup
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Configuration Checklist */}
      <Card className="border border-gray-200">
        <CardBody className="p-4">
          <h6 className="font-semibold text-gray-800 mb-3">
            📋 Configuration Checklist
          </h6>
          <div className="space-y-2">
            {[
              {
                label: "Source Directory Path",
                completed: !!job.dataSource.adk_processing?.sourceDirectory?.trim(),
                required: true,
              },
              {
                label: "Extraction Directory Path",
                completed: !!job.dataSource.adk_processing?.extractionPath?.trim(),
                required: true,
              },
              {
                label: "RAR Tool Path",
                completed: !!job.dataSource.adk_processing?.rarToolPath?.trim(),
                required: true,
              },
              {
                label: "File Name Prefixes",
                completed: (job.dataSource.adk_processing?.fileFilter?.startsWith?.length || 0) > 0,
                required: true,
              },
              {
                label: "Database Connection",
                completed: job.destination.type === "database" && 
                          !!job.destination.database?.host?.trim() &&
                          !!job.destination.database?.database?.trim(),
                required: true,
              },
              {
                label: "Processing Options",
                completed: job.dataSource.adk_processing?.processingOptions?.continueOnError === true,
                required: false,
              },
              {
                label: "Cleanup Configuration",
                completed: job.dataSource.adk_processing?.processingOptions?.deleteOldXmlFiles === true,
                required: false,
              },
            ].map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-2 rounded bg-gray-50">
                <div className={`w-4 h-4 rounded-full flex items-center justify-center ${
                  item.completed 
                    ? "bg-green-100 text-green-600" 
                    : item.required 
                    ? "bg-red-100 text-red-600" 
                    : "bg-gray-100 text-gray-400"
                }`}>
                  {item.completed ? (
                    <CheckCircle className="w-3 h-3" />
                  ) : (
                    <div className="w-2 h-2 rounded-full bg-current"></div>
                  )}
                </div>
                <span className={`text-sm ${
                  item.completed ? "text-gray-700" : item.required ? "text-red-600" : "text-gray-500"
                }`}>
                  {item.label}
                  {item.required && !item.completed && " (Required)"}
                  {!item.required && " (Recommended)"}
                </span>
              </div>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};
