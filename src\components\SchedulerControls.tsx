"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@heroui/react";
import { motion } from "framer-motion";
import { Play, Pause, Users } from "lucide-react";
import { JobStatus } from "@/types/job";

interface JobSequence {
  id: string;
  name: string;
  description: string;
  schedule?: string;
  enabled: boolean;
  onFailure: "stop" | "continue" | "retry";
  maxRetries: number;
  jobs: string[];
}

interface SchedulerControlsProps {
  jobs: JobStatus[];
  sequences: JobSequence[];
  onStopJob: (jobId: string) => void;
  onStartJob: (jobId: string) => void;
  onStopSequence: (sequenceId: string) => void;
  onStartSequence: (sequenceId: string) => void;
}

export const SchedulerControls = ({
  jobs,
  sequences,
  onStopJob,
  onStartJob,
  onStopSequence,
  onStartSequence,
}: SchedulerControlsProps) => {
  // Filter out jobs that are part of sequences
  const individualJobs = jobs.filter((job) => !job.sequenceConfig);
  return (
    <div>
      {/* Section Header - Outside the card like other sections */}
      <div className="flex items-center gap-[var(--spacing-sm)] mb-[var(--spacing-xl)]">
        <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center">
          <Play className="w-3 h-3 text-white" />
        </div>
        <h2 className="text-lg font-bold text-foreground">
          Scheduler Controls
        </h2>
      </div>

      <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl">
        <CardBody className="p-[var(--spacing-md)] space-y-[var(--spacing-lg)]">
          {/* Individual Jobs Section */}
          {individualJobs.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-3">
                <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <Play className="w-2 h-2 text-white" />
                </div>
                <h3 className="text-sm font-semibold text-foreground">
                  Individual Jobs
                </h3>
                <Chip
                  color="primary"
                  variant="flat"
                  size="sm"
                  className="text-xs"
                >
                  {individualJobs.length}
                </Chip>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
                {individualJobs.map((job, index) => (
                  <motion.div
                    key={`scheduler-${job.id}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.3,
                      delay: index * 0.1,
                      ease: "easeOut",
                    }}
                    whileHover={{
                      scale: 1.02,
                      transition: { duration: 0.2 },
                    }}
                  >
                    <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl transition-all duration-300 group">
                      <CardBody className="p-2">
                        <div className="flex flex-col gap-1.5">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <Tooltip
                                content={job.name}
                                placement="top"
                                delay={500}
                              >
                                <h3 className="font-medium text-foreground text-xs leading-tight truncate cursor-help">
                                  {job.name}
                                </h3>
                              </Tooltip>
                              <p className="text-xs text-muted-foreground mt-0.5 leading-tight">
                                {job.enabled
                                  ? "Scheduler Active"
                                  : "Scheduler Disabled"}
                              </p>
                            </div>
                            <motion.div
                              key={`scheduler-chip-${job.id}-${job.enabled}`}
                              initial={{ scale: 0.8, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ duration: 0.3 }}
                            >
                              <Chip
                                color={job.enabled ? "success" : "default"}
                                variant="flat"
                                size="sm"
                              >
                                {job.enabled ? "Enabled" : "Disabled"}
                              </Chip>
                            </motion.div>
                          </div>

                          <div className="flex justify-end">
                            {job.enabled ? (
                              <Button
                                size="sm"
                                color="warning"
                                variant="flat"
                                startContent={<Pause className="w-2.5 h-2.5" />}
                                onPress={() => onStopJob(job.id)}
                                className="group-hover:scale-105 transition-transform duration-200 text-xs h-6 min-h-6 px-2"
                              >
                                Disable
                              </Button>
                            ) : (
                              <Button
                                size="sm"
                                color="success"
                                variant="flat"
                                startContent={<Play className="w-2.5 h-2.5" />}
                                onPress={() => onStartJob(job.id)}
                                className="group-hover:scale-105 transition-transform duration-200 text-xs h-6 min-h-6 px-2"
                              >
                                Enable
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Job Sequences Section */}
          {sequences.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-3">
                <div className="w-4 h-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Users className="w-2 h-2 text-white" />
                </div>
                <h3 className="text-sm font-semibold text-foreground">
                  Job Sequences
                </h3>
                <Chip
                  color="secondary"
                  variant="flat"
                  size="sm"
                  className="text-xs"
                >
                  {sequences.length}
                </Chip>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
                {sequences.map((sequence, index) => (
                  <motion.div
                    key={`scheduler-sequence-${sequence.id}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.3,
                      delay: (individualJobs.length + index) * 0.1,
                      ease: "easeOut",
                    }}
                    whileHover={{
                      scale: 1.02,
                      transition: { duration: 0.2 },
                    }}
                  >
                    <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl transition-all duration-300 group border-l-4 border-l-purple-400">
                      <CardBody className="p-2">
                        <div className="flex flex-col gap-1.5">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1.5 mb-0.5">
                                <Users className="w-2.5 h-2.5 text-purple-600 flex-shrink-0" />
                                <Tooltip
                                  content={sequence.name}
                                  placement="top"
                                  delay={500}
                                >
                                  <h3 className="font-medium text-foreground text-xs leading-tight truncate cursor-help">
                                    {sequence.name}
                                  </h3>
                                </Tooltip>
                              </div>
                              <p className="text-xs text-muted-foreground leading-tight">
                                {sequence.enabled
                                  ? "Sequence Scheduler Active"
                                  : "Sequence Scheduler Disabled"}
                              </p>
                              <p className="text-xs text-purple-600 dark:text-purple-400 mt-0.5 leading-tight">
                                {sequence.jobs.length} job
                                {sequence.jobs.length !== 1 ? "s" : ""} in
                                sequence
                              </p>
                            </div>
                            <motion.div
                              key={`scheduler-sequence-chip-${sequence.id}-${sequence.enabled}`}
                              initial={{ scale: 0.8, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ duration: 0.3 }}
                            >
                              <Chip
                                color={sequence.enabled ? "success" : "default"}
                                variant="flat"
                                size="sm"
                              >
                                {sequence.enabled ? "Enabled" : "Disabled"}
                              </Chip>
                            </motion.div>
                          </div>

                          <div className="flex justify-end">
                            {sequence.enabled ? (
                              <Button
                                size="sm"
                                color="warning"
                                variant="flat"
                                startContent={<Pause className="w-2.5 h-2.5" />}
                                onPress={() => onStopSequence(sequence.id)}
                                className="group-hover:scale-105 transition-transform duration-200 text-xs h-6 min-h-6 px-2"
                              >
                                Disable
                              </Button>
                            ) : (
                              <Button
                                size="sm"
                                color="success"
                                variant="flat"
                                startContent={<Play className="w-2.5 h-2.5" />}
                                onPress={() => onStartSequence(sequence.id)}
                                className="group-hover:scale-105 transition-transform duration-200 text-xs h-6 min-h-6 px-2"
                              >
                                Enable
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Empty State */}
          {individualJobs.length === 0 && sequences.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Play className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No jobs or sequences available</p>
              <p className="text-xs">
                Create jobs to enable scheduler controls
              </p>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};
